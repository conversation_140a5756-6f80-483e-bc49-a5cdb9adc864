  .thumbnail-tight {
    margin-right: 0px; // Reduce spacing between thumbnail and label
  }


  .icon-wrapper {
    position: relative;
    width: 2.5rem;
    height: 2.5rem;
  
    .base-icon {
      font-size: 2.1rem;
      color: #444;
    }
  
    .star-icon,
    .wifi-icon {
      position: absolute;
      font-size: 0.9rem;
      color: orange;
    }
  
    .star-icon {
      top: -0.3rem;
      right: -0.3rem;
      color: orange;
    }
  
    .wifi-icon {
      top: -0.3rem;
      left: -0.8rem;
      color: orange;
    }

  }

    .group-header {
    --background: #3a3a3a; // dark gray background
    color: white;
    font-weight: bold;
  }
  
  .group-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
