<ion-header>
  <ion-toolbar color="primary" class="custom-toolbar">
    <ion-buttons slot="start">
      <ion-menu-button autoHide="false" class="white-icon"></ion-menu-button>
    </ion-buttons>
    <ion-title class="left-title">Forms</ion-title>
    <ion-button slot="end" (click)="getForms()">Get forms</ion-button>
  </ion-toolbar>

    <ion-toolbar color="primary" class="custom-toolbar">
     <ion-item>
   <ion-searchbar  color="light" placeholder="Filter" (ionInput)="onSearchChange($event.detail.value)" ></ion-searchbar>
   </ion-item>
<ion-item >
  <ion-grid>
    <ion-row>
      <!-- Full width on extra-small screens, half width on medium+ -->
      <ion-col size="12" size-md="6">
        <ion-toggle (ionChange)="onHideCompletedToggle($event)">Hide Completed Forms</ion-toggle>
      </ion-col>

      <ion-col size="12" size-md="6">
        <ion-toggle (ionChange)="onCategoryToggle($event)">Category</ion-toggle>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-item>

  
    </ion-toolbar>
</ion-header>

<ion-content class="ion-padding">
    <div style="height: 16px;"></div>
  <ng-container>
    <!-- If category toggle ON -->

    <ng-container *ngIf="categoryToggle">
      <ng-container *ngIf="groupedForms.length > 0">
        <ion-list *ngFor="let group of groupedForms">
          <ion-item-divider class="group-header">
            <ion-label>
              <div class="group-header-content">
                <span>{{ group.category }}</span>
                <span style="margin-left: 5px; margin-bottom: 2px;">({{ group.items.length }})</span>
              </div>
            </ion-label>
          </ion-item-divider>
          <ion-item *ngFor="let form of group.items" (click)="onFormSelect(form)" button>
            <ion-thumbnail slot="start" class="thumbnail-tight">
              <div class="icon-wrapper">
                <i class="fas fa-file-alt base-icon"></i>
              </div>
            </ion-thumbnail>
            <ion-label>
              <h2>{{ form.TEMPLATE_DESC }}</h2>
              <p>{{ form.FORM_ID }}</p>
              <p><strong>Created On:</strong> {{ form.CRTD_ON | customDate }}</p>
              <p><strong>Last Updated:</strong> {{ form.LAST_SYNC_TIME | customDate }}</p>
              <p>by {{ form.SUBM_BY }}</p>
            </ion-label>
            <ion-buttons slot="end">
              <ion-button *ngIf="form.FORM_STATUS == 'INPR'" color="warning" fill="solid" shape="round">
                {{ 'In progress' | translate }}
              </ion-button>
              <ion-button *ngIf="form.FORM_STATUS == 'SUBM'" color="success" fill="solid" shape="round">
                {{ 'Completed' | translate }}
              </ion-button>
            </ion-buttons>
          </ion-item>
        </ion-list>
        <div *ngIf="groupedForms.length === 0" style="text-align:center; color:gray; margin-top:2em;">
          No forms found.
        </div>
      </ng-container>
    </ng-container>

    <!-- If category toggle OFF -->
     <ng-container>
      
     </ng-container>
    <ng-container *ngIf="!categoryToggle ">
      <ion-list>
                 <ion-item-divider class="group-header">
            <ion-label>
              <div class="group-header-content">
                <span>Forms </span>
                <span style="margin-left: 5px; margin-bottom: 2px;" *ngIf="plainList &&  plainList.length != 0">({{ plainList.length }})</span>
              </div>
            </ion-label>
          </ion-item-divider>
        <ion-item *ngFor="let form of plainList" (click)="onFormSelect(form)" button>
          <ion-thumbnail slot="start" class="thumbnail-tight">
            <div class="icon-wrapper">
              <i class="fas fa-file-alt base-icon"></i>
            </div>
          </ion-thumbnail>
          <ion-label>
            <h2>{{ form.TEMPLATE_DESC }}</h2>
            <p>{{ form.FORM_ID }}</p>
            <p><strong>Created On:</strong> {{ form.CRTD_ON | customDate }}</p>
            <p><strong>Last Updated:</strong> {{ form.LAST_SYNC_TIME | customDate }}</p>
            <p>by {{ form.SUBM_BY }}</p>
          </ion-label>
          <ion-buttons slot="end">
            <ion-button *ngIf="form.FORM_STATUS == 'INPR'" color="warning" fill="solid" shape="round">
              {{ 'In progress' | translate }}
            </ion-button>
            <ion-button *ngIf="form.FORM_STATUS == 'SUBM'" color="success" fill="solid" shape="round">
              {{ 'Completed' | translate }}
            </ion-button>
          </ion-buttons>
        </ion-item>
      </ion-list>
      <div *ngIf="plainList &&  plainList.length === 0" style="text-align:center; color:gray; margin-top:2em;">
        No forms found.
      </div>
    </ng-container>
  </ng-container>
  <ion-content>

</ion-content>

</ion-content>
