import { Component, NgZone, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonButton, IonButtons, IonCheckbox, IonContent, IonHeader, IonIcon, IonItem, IonLabel, IonList, IonSearchbar, IonTitle, IonToolbar, ModalController } from '@ionic/angular/standalone';
import { AppConstants } from 'src/app/constants/appConstants';
import {arrowBackOutline} from 'ionicons/icons';
import { addIcons } from 'ionicons';
addIcons({'arrow-back-outline': arrowBackOutline});

@Component({
  selector: 'app-select-list',
  templateUrl: './select-list.page.html',
  styleUrls: ['./select-list.page.scss'],
  standalone: true,
  imports: [IonContent, IonHeader, IonTitle, IonToolbar, CommonModule, FormsModule, IonButton, IonButtons, IonSearchbar, IonList, IonItem, IonLabel, IonCheckbox, IonIcon]
})
export class SelectListPage implements OnInit {
  // @ViewChild('searchBar', { static: true }) searchBar!: ElementRef;

  listData: any[] = []; // Provide this via input or on init
  displayListData: any[] = [];
  radioItemSelected: string = '';
  styleTheme: string = 'normal';
  hasError: boolean = AppConstants.BOOL_FALSE;

  listDetails = {
    title: 'Select List',
    searchPlaceholder: 'Search List',
    selectKey: 'isSelected',
    multiSelect: AppConstants.BOOL_FALSE,
    hideCloseBtn: AppConstants.BOOL_FALSE,
    searchValue: ''
  };

  constructor(private ngZone: NgZone,
     private modalController: ModalController,
  ) {
    // Mock initial data
    this.listData = [
      { DisplayString: 'Item 1', object: {} },
      { DisplayString: 'Item 2', object: {} }
    ];
    this.displayListData = [...this.listData];

    this.listData.forEach((item, i) => {
      item.data_id = 'data-' + (i + 1);
      item[this.listDetails.selectKey] = AppConstants.BOOL_FALSE;
    });
  }
           
  ngOnInit(): void {
    
  }

  // ngAfterViewInit() {
  //   this.searchBar.nativeElement.querySelector('input').focus();
  // }

  toggleSelectData(data: any) {
    if (this.listDetails.multiSelect) {
      data[this.listDetails.selectKey] = !data[this.listDetails.selectKey];
      this.hasError = this.noDataSelected();
    }
  }

  toggleRadioData(data: any) {
    if (!this.listDetails.multiSelect) {
      this.radioItemSelected = data.DisplayString;
      this.listData.forEach(d => d[this.listDetails.selectKey] = false);
      data[this.listDetails.selectKey] = AppConstants.BOOL_TRUE;
      this.publishEvent(data);
    }
  }

  noDataSelected(): boolean {
    return !this.listData.some(d => d[this.listDetails.selectKey]);
  }

  saveSelection(newEntry?: boolean) {
    let dataToSend = this.listDetails.multiSelect
      ? this.listData
      : newEntry
      ? { newEntry: true, value: this.listDetails.searchValue?.substring(0, 50) }
      : null;
    this.publishEvent(dataToSend);
  }

  publishEvent(data: any) {
    // In Ionic 8, you'd use modalController.dismiss or router navigate
    console.log('Selected:', data);
    this.modalController.dismiss();
  }

  filterList(searchText: string) {
    const txt = searchText.trim().toLowerCase();
    this.ngZone.run(() => {
      this.displayListData = this.listData.filter(item =>
        item.DisplayString?.toLowerCase().includes(txt)
      );
      this.hasError = this.noDataSelected();
    });
  }

  
  close() {
    this.modalController.dismiss();
  }
}
