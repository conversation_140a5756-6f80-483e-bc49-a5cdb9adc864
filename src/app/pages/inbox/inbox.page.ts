import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonButtons, IonContent, IonHeader, IonMenuButton, IonRouterOutlet, IonTitle, IonToolbar, MenuController, IonButton } from '@ionic/angular/standalone';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';


@Component({
  selector: 'app-inbox',
  templateUrl: './inbox.page.html',
  styleUrls: ['./inbox.page.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule , IonToolbar, IonContent, IonButtons, IonTitle ,IonHeader, IonMenuButton, IonButton]
})
export class InboxPage implements OnInit {

  constructor( private routerOutlet: IonRouterOutlet,
      private menuCtrl: MenuController, private unviredSDK: UnviredCordovaSDK) { 
       this.routerOutlet.swipeGesture = false;
      this.menuCtrl.swipeGesture(true)
  }

  ngOnInit() {
  }

  sendLogs() {
    (async () => {
      try {
        console.log("Sending logs to server...");
        await this.unviredSDK.sendLogToServer();
        console.log("Logs sent successfully.");
      } catch (error) {
        console.error('Error sending logs:', error);
      }
    })();
  }

}
