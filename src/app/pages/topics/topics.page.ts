import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { IonicModule, AlertController, NavController, Platform } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { ActivatedRoute } from '@angular/router';
import { AppConstants } from '../../constants/appConstants';
import { AlertService } from 'src/app/services/alert.service';
import { UtilityService } from 'src/app/services/utility.service';
import { DataService } from 'src/app/services/data.service';
import { FormUtilityService } from 'src/app/services/formUtility.service';
import { TOPIC_HEADER } from 'src/models/TOPIC_HEADER';
import { CTA_HEADER } from 'src/models/CTA_HEADER';
import { STMR_TOPIC } from 'src/models/STMR_TOPIC';
import { TMPLT_VER } from 'src/models/TMPLT_VER';
import { TMPLT_ASSGN } from 'src/models/TMPLT_ASSGN';
import { STMR_FORM_DATA } from 'src/models/STMR_FORM_DATA';
import { STMR_FORM } from 'src/models/STMR_FORM';
import { EventsService } from 'src/app/services/events.service';
import { ModalController } from '@ionic/angular/standalone';
import moment from 'moment';
import { SubmitSTMRService } from 'src/app/services/submitSTMR.service';
import { STMR_ACTION } from 'src/models/STMR_ACTION';
import { STMR_HEADER } from 'src/models/STMR_HEADER';
import { FormsPage } from '../forms/forms.page';
import { DisplayCTAService } from 'src/app/services/displayCTA.service';
import { STMR_TOPIC_ENTITY } from 'src/models/STMR_TOPIC_ENTITY';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { selectPrefilledData } from 'src/app/store/store.selector';
import { TemplatesPage, ModeOfOperation } from '../templates/templates.page';
import { STMRDetailsPage } from '../stmr-details/stmr-details.page';
import { HSE_STANDARD_HEADER } from 'src/models/HSE_STANDARD_HEADER';
import { DisplayHSEService } from 'src/app/services/displayHSE.service';
import { unzipSync } from 'fflate';
import { SelectListPage } from '../select-list/select-list.page';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { firstValueFrom } from 'rxjs';
import { filter } from 'rxjs/operators';


declare var Windows: any;


@Component({
  selector: 'app-topics',
  templateUrl: './topics.page.html',
  styleUrls: ['./topics.page.scss'],
  standalone: true,
  imports: [IonicModule, CommonModule, FormsModule, TranslateModule]
})
export class TopicsPage implements OnInit, OnDestroy {

  @ViewChild('navBar') navBar: any;

  topicType: string = '';
  isEditTopic: boolean = false

  topics: TOPIC_HEADER[] = [];
  ctas: CTA_HEADER[] = [];
  hses: HSE_STANDARD_HEADER[] = [];
  topicEntity: STMR_TOPIC_ENTITY = new STMR_TOPIC_ENTITY();
  ctaDocs: any[] = [];
  hseDocs: any[] = [];
  ctaTmplt: any[] = [];

  templates: any[] = [];
  tempVer: TMPLT_VER[] = [];
  tempAssign: TMPLT_ASSGN[] = [];
  ctaAssign: any[] = [];

  returnDisplayDate: any;
  myBrowser: any;
  currStmrHeader: any;
  stmrActionArray: STMR_ACTION[] = [];

  newTopicDesc: string = "";
  newCtaDesc: string = "";
  newhseDesc: string = '';
  styleTheme: string = "normal";
  prefillData$!: Observable<any>; // for async pipe OR manual subscription
  prefillData: any;  

  homePages: any;
  timestampOfLastTap: number = 0
  isTopicUpdated: boolean = false
  placeholderModel = "Description / Additional Comments"
  isFirst: boolean = true
  indexOfLastOpenedFormData: number = -1;
  indexOfLastOpenedForm: number = -1;
  topicStart: string = new Date().toISOString();
  TOPIC_NOTE :string = "";
  defaultDescription: string = '';
  minDate: Date = new Date();       
  maxDate: Date = new Date();
  errorMessageForInvalidDateTime: string = '';
  currentTimePast24HrsFromSTMRCreatedTime: boolean = false;
  intervalId:any;
  stmrCreateDate : any;
  STMRDetailsPage: any;
  time_now = new Date(Date.now());
  maxAllowedValue: string = '';
  maxAllowedTopicTime: any;
  monthValue: number = 0;
  dayValue: number = 0;
  yearValue: number = 0;
  hourValue: number = 0;
  minValue: number = 0;
  stmrCreatedTime: string = '';
  exceedMaxTimeErr :boolean = false;
  belowSTMRTimeErr : boolean = false;
  monthNames = ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'];

  // Inject unviredSDK instead of using global ump
  constructor(
    private unviredSDK: UnviredCordovaSDK,
    public translate: TranslateService,
    public navCtrl: NavController,
    public alertService: AlertService,
    public ngZone: NgZone,
    public dataService: DataService,
    private iab: InAppBrowser,
    public alertCtrl: AlertController,
    public platform: Platform,
    public alertController: AlertController,
    private file: File,
    public formUtilityService: FormUtilityService,
    public submitSTMRService: SubmitSTMRService,
    private displayCTAService: DisplayCTAService,
    private displayHSEService: DisplayHSEService,
    private utilityService: UtilityService,
    private route: ActivatedRoute,
    private store: Store<any>,
    private modalController: ModalController
  ) {
    this.returnDisplayDate = this.utilityService.returnDisplayDate;
  }

 async ngOnInit() {
  this.prefillData$ = this.store.select(selectPrefilledData);
  const routeData = this.route.snapshot.data;

  this.homePages = routeData['rootName'];
  this.STMRDetailsPage = routeData['STMRDetailsPage'];
  const topicData = routeData['data'];
  this.styleTheme = routeData['theme'];
  this.topicType = topicData.type;
  this.currStmrHeader = topicData.stmrHeader;

  // minDate & maxDate setup for Ionic picker
  this.minDate = new Date(this.currStmrHeader.CRTD_ON * 1000);
  this.computeMaxDate();

  this.currentTimePast24HrsFromSTMRCreatedTime = this.isCurrentTimePast24HrsFromSTMRCreatedTime();

  if (this.topicType === 'Edit') {
    await this.initEditTopic(topicData.topic);
  } else {
    this.initNewTopic(topicData.existingTopics);
  }

  await this.getTopicsFromDB();
  this.setSTMRCreateDate();
  this.getMaxTopicTime();
}

private async initEditTopic(topic: STMR_TOPIC_ENTITY) {
  this.topicEntity = topic;

  this.ngZone.run(async () => {
    if (this.topicEntity.topic.P_MODE !== 'A') {
      this.topicEntity.topic.P_MODE = 'M';
    }

    if (this.topicEntity.topic.CTA_ID) {
      this.defaultDescription = await this.getCTANameFromDB(this.topicEntity.topic.CTA_ID);
      this.TOPIC_NOTE = this.topicEntity.topic.TOPIC_NOTE.substr(this.defaultDescription.length + 1);
    } else if (this.topicEntity.topic.STD_ID) {
      this.defaultDescription = await this.getHSENameFromDB(this.topicEntity.topic.STD_ID);
      this.TOPIC_NOTE = this.topicEntity.topic.TOPIC_NOTE.substr(this.defaultDescription.length + 1);
    } else {
      this.TOPIC_NOTE = this.topicEntity.topic.TOPIC_NOTE;
    }
  });

  this.topicStart = this.normalizeTopicStart(this.topicEntity.topic.TOPIC_START);
  this.topicEntity.forms = this.getFormsWithSyncAndFormStatuses(this.topicEntity);
  this.isEditTopic = AppConstants.BOOL_TRUE;
}

private initNewTopic(existingTopics: STMR_TOPIC_ENTITY[]) {
  const newTopic = new STMR_TOPIC();
  newTopic.LID = this.utilityService.guid32();
  newTopic.TOPIC_NOTE = '';
  newTopic.CTA_ID = '';
  newTopic.P_MODE = 'A';

  this.topicEntity = new STMR_TOPIC_ENTITY();
  this.topicEntity.topic = newTopic;

  const sortedArray = [...existingTopics].sort((a, b) => 
    parseInt(a.topic.TOPIC_NO) - parseInt(b.topic.TOPIC_NO)
  );

  this.topicEntity.topic.TOPIC_NO = sortedArray.length > 0
    ? (parseInt(sortedArray[sortedArray.length - 1].topic.TOPIC_NO) + 1).toString()
    : '1';

  this.topicStart = moment().local().format("YYYY-MM-DDTHH:mmZ");
}



private normalizeTopicStart(startValue: string): string {
  const date = isNaN(Number(startValue))
    ? new Date(startValue)
    : new Date(Number(startValue) * 1000);
  return moment(date).local().format('YYYY-MM-DDTHH:mmZ');
}


  ngOnDestroy() {
    // Clean up intervals
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  isSTMRReadonly(stmr: any): boolean {
  if (!stmr || !stmr.STMR_STATUS) {
    return false; // Or true, depending on default logic
  }
  return stmr.STMR_STATUS === 'READONLY';
}

  getFormsWithSyncAndFormStatuses(topicEntity: STMR_TOPIC_ENTITY): any[] {
    return topicEntity.forms.map(form => {
      return {
        ...form,
      formStatus: this.utilityService.getFormStatusObj(form.FORM_STATUS),
        syncStatus: (() => {
          const tmp = this.utilityService.getSyncStatusObj(form.SYNC_STATUS);
          return (tmp.descr && tmp.color) ? tmp : undefined;
        })()
      };
    });
  }

  ionViewDidLoad() {
    console.log('ionViewDidLoad TopicsPage');
    //  Prompt to ask if form needs to be SAVED.

    this.store.select(selectPrefilledData).subscribe(data => {
      this.prefillData = data;
      // Any extra logic can go here...
    });

    /**
     * Test Automation mode, lets add a name for the back button so that it can be accessed.
     */
    this.utilityService.isAppRunningInTestAutomationMode().then(isTestMode => {
      if (isTestMode) {
        // Modern Ionic doesn't have setBackButtonText, use ion-back-button instead
        console.log('Test mode enabled');
      }
    })

    // Modern Angular/Ionic navigation handling
    this.setupBackButtonHandler();
  }

  private setupBackButtonHandler() {
    // Handle back button logic in modern Angular/Ionic
    if (this.isTopicUpdated) {
      let message = this.translate.instant("Your unsaved changes will be lost if you don't save them.")
      this.translate.get(message).subscribe(value => {
        message = value
      })
      
      this.showBackConfirmationAlert(message);
    } else {
      this.navCtrl.back();
    }
  }

  private async showBackConfirmationAlert(message: string) {
    const alert = await this.alertCtrl.create({
      header: this.translate.instant('Are you sure you want to go back?'),
      subHeader: message,
      buttons: [
        {
          text: this.translate.instant('Save'),
          handler: () => {
            this.saveTopicInDB().then(() => {
              this.navCtrl.back()
            })
          }
        },
        {
          text: this.translate.instant("Don't Save"),
          handler: () => {
            this.navCtrl.back();
          }
        },
        {
          text: this.translate.instant('Cancel'),
          handler: () => {
            // Nothing to do here.
          }
        }]
    });
    await alert.present();
  }


  saveTopicState(): Promise<any> {
  return new Promise(async (resolve) => {
    await this.saveFormData();

    // Always safe: topicStart is ISO string
    const date = new Date(this.topicStart).getTime() / 1000;
    this.topicEntity.topic.TOPIC_START = String(date);

    // Topic Note
    const note = (this.TOPIC_NOTE ?? '').trim();
    this.topicEntity.topic.TOPIC_NOTE = this.defaultDescription
      ? `${this.defaultDescription} ${note}`
      : note;

    // Topic Name
    this.topicEntity.topic.TOPIC_NAME = this.utilityService.getTopicName(
      this.topics,
      this.topicEntity.topic.TOPIC_ID
    );

    // Object Status update
    if (this.topicEntity.topic.OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
      this.topicEntity.topic.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
    }

    // Callback
    this.STMRDetailsPage.callbackAfterUserClosedAppInTopssicsPage(
      this.currStmrHeader,
      JSON.parse(JSON.stringify(this.topicEntity)),
      JSON.parse(JSON.stringify(this.stmrActionArray))
    );

    resolve(this.STMRDetailsPage.saveSTMRState());
  });
}


  saveFormData(): Promise<void> {
    var that = this
    return new Promise(async (resolve, reject) => {

      if (that.indexOfLastOpenedForm == -1 || that.indexOfLastOpenedFormData == -1) {
        console.log("TopicsPage", "saveFormData()", "No STMR form open. Nothing to save in STMR form.")
        return resolve()
      }

      console.log("TopicsPage", "saveFormData()", "STMR Form Index: " + that.indexOfLastOpenedForm + " STMR Data Index: " + that.indexOfLastOpenedFormData)

      // Read Form Data
      try {
        const result = await that.myBrowser.executeScript({
          code: "generateJSON()"
        });
        if (result && result.length > 0) {
          var formData = result[0]
          // Form Header with FORM_STATUS
          // If isSubmit is true the set status as AppConstants.VAL_FORM_STATUS.SUBM
          let status = AppConstants.VAL_FORM_STATUS.INPR

          // Update STMR Form
          that.topicEntity.forms[that.indexOfLastOpenedForm].FORM_STATUS = status
          that.topicEntity.forms[that.indexOfLastOpenedForm].TIME_ZONE = that.utilityService.getTimezone();
          
          if (that.topicEntity.forms[that.indexOfLastOpenedForm].OBJECT_STATUS == AppConstants.OBJECT_STATUS.GLOBAL) {
            that.topicEntity.forms[that.indexOfLastOpenedForm].OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY
            that.topicEntity.forms[that.indexOfLastOpenedForm].P_MODE = 'M'
          }

          // Update STMR Form Data
          that.topicEntity.data[that.indexOfLastOpenedFormData].DATA = formData
          if (that.topicEntity.data[that.indexOfLastOpenedFormData].OBJECT_STATUS == AppConstants.OBJECT_STATUS.GLOBAL) {
            that.topicEntity.data[that.indexOfLastOpenedFormData].OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY
            that.topicEntity.data[that.indexOfLastOpenedFormData].P_MODE = 'M'
          }
          return resolve()
        }
        else {
          console.error("TopicsPage", "saveFormData()", "Empty result obtained from inAppBrowser")
          return resolve()
        }
      } catch (error) {
        console.error("TopicsPage", "saveFormData()", "Error while reading JSON from In-App Browser: " + error)
        return resolve()
      }
    })
  }

  addFormsToTopic() {

    // Validate Topics before adding forms
    if (!this.validateTopic()) {
      console.log("TopicsPage", "addFormsToTopic()", "Topic is not validated. Asking the user to complete topic details.")
      return
    }

    this.navCtrl.navigateForward('/templates', {
      state: {
        modeOfOperation: ModeOfOperation.AddFormToTopic
      }
    })
  }

 onDatetimeChange(event: any) {
  this.ngZone.run(() => {
    const selectedDate = new Date(event.detail.value); // always create Date object
    const selDate = Math.floor(selectedDate.getTime() / 1000); // Unix seconds

    const createdOn = Math.floor(this.minDate.getTime() / 1000);
    const maxTimestampSeconds = Math.floor(this.maxDate.getTime() / 1000);

    if (selDate < createdOn) {
      this.isTopicUpdated = false;
      const displayDate = this.utilityService.returnDisplayDate(this.minDate, "", "MMM DD YYYY HH:mm");
      this.errorMessageForInvalidDateTime = this.translate.instant(
        `Topic time cannot be earlier than STMR's created date: ${displayDate}`
      );
    } else if (selDate > maxTimestampSeconds) {
      this.isTopicUpdated = false;
      const displayDate = this.utilityService.returnDisplayDate(this.maxDate, "", "MMM DD YYYY HH:mm");
      this.errorMessageForInvalidDateTime = this.translate.instant(
        `Topic time cannot be adjusted past the current date: ${displayDate}`
      );
    } else {
      this.errorMessageForInvalidDateTime = '';
      this.isTopicUpdated = true;

      // Store ISO string in topicStart (for UI binding)
      this.topicStart = selectedDate.toISOString();

      // Store Unix timestamp string in topic entity
      this.topicEntity.topic.TOPIC_START = selDate.toString();
    }
  });
}


  ionViewWillEnter() {
    console.log('ionViewWillEnter TopicsPage');
    let that = this;
  }

  async getTopicsFromDB() {
    let that = this;
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_TOPIC_HEADER, {'IS_ACTIVE': 'true'});
      if (result.data && result.data.length > 0) {
        that.topics = result.data;
        this.ngZone.run(() => {
          this.topics = this.utilityService.sortArray(this.topics, 'TOPIC_ID');
          if (this.topicEntity.topic.TOPIC_ID) this.newTopicDesc = this.utilityService.getTopicName(this.topics, this.topicEntity.topic.TOPIC_ID);
        });
      }
    } catch (error) {
      console.error("TopicsPage", "getTopicsFromDB", "Error while fetching Topics from DB : " + JSON.stringify(error));
      that.alertService.showAlert("Error", JSON.stringify(error));
    }
    this.getCTAAssignsFromDB();
    this.getHSEFormDB();
  }

  async getCTAAssignsFromDB() {
    let that = this;
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_CTA_ASSIGNMENT, {});
      if (result.data && result.data.length > 0) {
        that.ctaAssign = result.data;
        this.getCTAsFromDB();
      }
    } catch (error) {
      console.error("TopicsPage", "getCTAsFromDB", "Error while fetching CTAs from DB : " + JSON.stringify(error));
      that.alertService.showAlert("Error", JSON.stringify(error));
    }
    if (this.isEditTopic) {
      this.getDocsAndTemplates(AppConstants.BOOL_TRUE)
    }
    else {
      this.getDocsAndTemplates();
    }
  }

  async getCTAsFromDB() {
    let that = this;
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_CTA_HEADER, {});
      if (result.data && result.data.length > 0) {
        // Filter ctas by CTA_ASSGN
        // 1. Group assignments by CTA_ID
        // 2. Match (rig and my rig) or no rig
        // 3. Delete other CTAs that dont match

        var tempCTAs = JSON.parse(JSON.stringify(result.data)),
          temp = [],
          hasOneRig = AppConstants.BOOL_FALSE,
          hasMyRig = AppConstants.BOOL_FALSE;

        that.ctas = result.data;

        this.getRigDetFromDB((rigDet) => {
          for (let i = 0, iLen = tempCTAs.length; i < iLen; i++) {
            temp = [];
            for (let j = 0, jLen = that.ctaAssign.length; j < jLen; j++) {
              if (that.ctaAssign[j].CTA_ID == tempCTAs[i].CTA_ID) {
                temp.push(that.ctaAssign[j]);
              }
            }

            // Check Rig Assignment
            hasOneRig = AppConstants.BOOL_FALSE;
            hasMyRig = AppConstants.BOOL_FALSE;
            for (let l = 0, lLen = temp.length; l < lLen; l++) {
              hasOneRig = AppConstants.BOOL_TRUE; // Has one assignment
              if (temp[l].RIG_TYPE == rigDet.RIG_TYPE && temp[l].RIG_SUB_TYPE == rigDet.RIG_SUB_TYPE) {
                hasMyRig = AppConstants.BOOL_TRUE;
              }
            }

            // If rig then one rig must be logged in user rig
            if (hasOneRig && hasMyRig) {
              // when has rig assignment
              tempCTAs[i].isValidTmplt = AppConstants.BOOL_TRUE;

            } else if (!hasOneRig) {
              // when no rig assignments found - display this template
              tempCTAs[i].isValidTmplt = AppConstants.BOOL_TRUE;

            } else {

              tempCTAs[i].isValidTmplt = AppConstants.BOOL_FALSE;
            }

            if (tempCTAs[i].isValidTmplt == AppConstants.BOOL_FALSE) {
              for (let m = that.ctas.length - 1; m >= 0; --m) {
                if (that.ctas[m].CTA_ID == tempCTAs[i].CTA_ID) {
                  that.ctas.splice(m, 1);
                }
              }
            }

          }

          this.ngZone.run(() => {
            this.ctas = this.utilityService.sortArray(this.ctas, 'DESCR');
            if (this.topicEntity.topic.CTA_ID) this.newCtaDesc = this.utilityService.getCTADesc(this.ctas, this.topicEntity.topic.CTA_ID);
          })
        })
      }
    } catch (error) {
      console.error("TopicsPage", "getCTAsFromDB", "Error while fetching CTAs from DB : " + JSON.stringify(error));
      that.alertService.showAlert("Error", JSON.stringify(error));
    }
  }

  // Get forms and documents from Db
  async getDocsAndTemplates(onlyThis?: boolean) {
    let that = this;
    // Documents
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_CTA_DOC, { 'CTA_ID': that.topicEntity.topic.CTA_ID });
      if (result.data && result.data.length > 0) {
        that.ctaDocs = result.data;
      }
    } catch (error) {
      console.error("TopicsPage", "getDocsAndTemplates", "Error while fetching CTA Documents from DB : " + JSON.stringify(error));
      that.alertService.showAlert("Error", JSON.stringify(error));
    }

    if (!onlyThis) this.getTemplatesHierarchy();
  }

  // Get Templates Hierarchy
  getTemplatesHierarchy() {
    let that = this;
    that.getTemplatesFromDB((res) => {
      that.ngZone.run(() => {
        that.ctaTmplt = res;

        // Create Topic & Forms from templates.
        that.topicEntity.topic = this.createNewTopic(that.topicEntity.topic)
        that.topicEntity.forms = this.createSTMRForms(that.topicEntity.topic, that.ctaTmplt);
        that.topicEntity.data = this.createSTMRFormdata(that.topicEntity.forms)
      })
    });
  }

  async getHSEFormDB() {
    let that = this;
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_HSE_STANDARD_HEADER, {});
      if (result.data && result.data.length > 0) {
        that.hses = result.data; 
        that.ngZone.run(() => {
          that.hses = that.utilityService.sortArray(that.hses, 'DESCR');
          if (that.topicEntity.topic.STD_ID) that.newhseDesc = that.utilityService.getHSEDesc(that.hses, that.topicEntity.topic.STD_ID);
        })
      }
    } catch (error) {
      console.error("TopicsPage", "getHSEFormDB", "Error while fetching HSE Standards from DB : " + JSON.stringify(error));
      that.alertService.showAlert("Error", JSON.stringify(error));
    }
    this.getHSEDocs()    
  }

  // Get forms and documents from Db
  async getHSEDocs() {
    let that = this;
    // Documents
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_HSE_STANDARD_DOC, { 'STD_ID': that.topicEntity.topic.STD_ID });
      if (result.data && result.data.length > 0) {
        that.hseDocs = result.data;
      }
    } catch (error) {
      console.error("TopicsPage", "getHSEDocs", "Error while fetching HSE Documents from DB : " + JSON.stringify(error));
      that.alertService.showAlert("Error", JSON.stringify(error));
    }
  }


  createSTMRFormdata(STMRForms: STMR_FORM[]): STMR_FORM_DATA[] {
    var formDataArray: STMR_FORM_DATA[] = []
    STMRForms.forEach(element => {
      let formData = new STMR_FORM_DATA()
      formData.LID = this.utilityService.guid32();
      formData.FID = element.FID
      formData.STMR_ID = element.STMR_ID
      formData.TOPIC_NO = element.TOPIC_NO
      formData.FORM_ID = element.FORM_ID
      formData.DATA = ''
      formData.P_MODE = 'A'
      formData.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD
      formDataArray.push(formData)
    });
    return formDataArray
  }

  // DB Call to get Templates
  async getTemplatesFromDB(callback: (data: any[]) => void) {
    let that = this;
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_CTA_TMPLT, {});
      if (result.data && result.data.length > 0) {
        that.templates = result.data;
      }
      that.getTemplatesVerFromDB(callback);
    } catch (error) {
      console.error("TopicsPage", "getTemplatesFromDB", "Error while fetching CTA Templates from DB : " + JSON.stringify(error));
      that.alertService.showAlert("Error", JSON.stringify(error));
      callback([]);
    }
  }

  // DB Call to get Template Version.
  async getTemplatesVerFromDB(callback: (data: any[]) => void) {
    let that = this;
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_TEMPLATE_VERSION, {});
      if (result.data && result.data.length > 0) {
        that.tempVer = result.data;
      }
      that.getTemplatesAssgnFromDB(callback);
    } catch (error) {
      console.error("TopicsPage", "getTemplatesVerFromDB", "Error while fetching Templates Version from DB : " + JSON.stringify(error));
      that.alertService.showAlert("Error", JSON.stringify(error));
      callback([]);
    }
  }

  // DB Call to get Template Assign.
  async getTemplatesAssgnFromDB(callback: (data: any[]) => void) {
    let that = this;
    try {
      const result = await this.unviredSDK.dbExecuteStatement("SELECT * FROM TMPLT_ASSGN");
      if (result.data && result.data.length > 0) {
        that.tempAssign = result.data;
      }
      that.mapTemplVer(that.tempVer, callback);
    } catch (error) {
      console.error("TopicsPage", "getTemplatesAssgnFromDB", "Error while fetching Templates Assign from DB : " + JSON.stringify(error));
      that.alertService.showAlert("Error", JSON.stringify(error));
      callback([]);
    }
  }

  /**
 * Map Template Version to Templates Header
 * matching TMPLT_ID
 */
  mapTemplVer(array: TMPLT_VER[], callback: (data: any[]) => void) {
    let that = this,
      temp = [];
    if (that.templates && that.templates.length) {
      if (array && array.length) {
        for (let i = 0, iLen = that.templates.length; i < iLen; i++) {
          // that.templates[i][AppConstant.TABLE_TEMPLATE_VERSION] = [];
          temp = [];
          for (let j = 0, jLen = array.length; j < jLen; j++) {
            if (array[j].TMPLT_ID == that.templates[i].TEMPLATE_ID) {
              temp.push(array[j]);
            }
          }
          // that.templates[i][AppConstant.TABLE_TEMPLATE_VERSION] = temp;
          //Fetch latest L_VER_NO, L_VER_ID, L_CRTD_BY and L_CRTD_ON
          let latest: { [key: string]: any } = this.utilityService.getReleaseVersionDet(temp, [AppConstants.VER_NO, AppConstants.VER_ID, AppConstants.CRTD_BY, AppConstants.CRTD_ON])
          // Prefix "L_" to the string
          for (let key in latest) {
            that.templates[i]["L_" + key] = latest[key];
          }
        }
      }
      that.mapTemplAssgn(that.tempAssign, callback);
    } else {
      callback([]);
    }
  }

	/**
	 * Map Template Assign to Templates Header
	 * matching TMPLT_ID
	 */
  mapTemplAssgn(array: TMPLT_ASSGN[], callback: (data: any[]) => void) {
    let that = this,
      temp = [];
    if (that.templates && that.templates.length) {
      if (array && array.length) {
        for (let i = 0, iLen = that.templates.length; i < iLen; i++) {
          that.templates[i][AppConstants.TABLE_TEMPLATE_ASSIGN] = [];
          temp = [];
          for (let j = 0, jLen = array.length; j < jLen; j++) {
            if (array[j].TMPLT_ID == that.templates[i].TEMPLATE_ID) {
              temp.push(array[j]);
            }
          }
          that.templates[i][AppConstants.TABLE_TEMPLATE_ASSIGN] = temp;
        }
      }
    }
    // Remove templates without assignment
    for (let j = that.templates.length - 1; j >= 0; j--) {
      if (!that.templates[j].TMPLT_ASSGN || !that.templates[j].TMPLT_ASSGN.length) {
        that.templates.splice(j, 1);
      }
    }
    callback(that.templates);
  }

  // User tapping Save Button.
  saveTopic() {
    // Ignore double taps within 'x' seconds.
    let timestamp = new Date().getTime()
    if (timestamp - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
    this.unviredSDK.logInfo("TemplatesPage", "createSTMR()", "Ignoring double tap")
      return
    }
    this.timestampOfLastTap = timestamp

    this.saveTopicInDB()
  }

  saveTopicInDB(): Promise<void> {
  return new Promise((resolve, reject) => {
    if (!this.validateTopic()) {
      reject();
      return;
    }

    const date = new Date(this.topicStart).getTime() / 1000;
    this.topicEntity.topic.TOPIC_START = String(date);

    this.topicEntity.topic.TOPIC_NAME = this.utilityService.getTopicName(
      this.topics,
      this.topicEntity.topic.TOPIC_ID
    );

    if (this.topicEntity.topic.OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
      this.topicEntity.topic.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
    }

    const note = (this.TOPIC_NOTE ?? '').trim();
    this.topicEntity.topic.TOPIC_NOTE = this.defaultDescription
      ? `${this.defaultDescription} ${note}`
      : note;

    this.STMRDetailsPage.callbackAfterTopic(
      this.currStmrHeader,
      JSON.parse(JSON.stringify(this.topicEntity)),
      JSON.parse(JSON.stringify(this.stmrActionArray))
    );

    this.isTopicUpdated = false;
    resolve();
  });
}


  createNewTopic(tempTopic: STMR_TOPIC) {
    let that = this,
      itemStmrTopic: STMR_TOPIC = <STMR_TOPIC>{};

    itemStmrTopic.STMR_ID = that.currStmrHeader.STMR_ID;
    itemStmrTopic.CTA_ID = tempTopic.CTA_ID;
    itemStmrTopic.TOPIC_NO = tempTopic.TOPIC_NO + "";
    itemStmrTopic.TOPIC_ID = tempTopic.TOPIC_ID;
    itemStmrTopic.TOPIC_START = tempTopic.TOPIC_START;
    itemStmrTopic.TOPIC_NOTE = tempTopic.TOPIC_NOTE;
    itemStmrTopic.TOPIC_NAME = tempTopic.TOPIC_NAME;
    itemStmrTopic.P_MODE = tempTopic.P_MODE
    itemStmrTopic.FID = that.currStmrHeader.LID;
    if (tempTopic.LID) itemStmrTopic.LID = tempTopic.LID;

    if (this.isEditTopic) {
      itemStmrTopic.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
    } else {
      itemStmrTopic.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
      itemStmrTopic.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
    }
    return itemStmrTopic
  }

  // Create or update form header
  createSTMRForms(newTopic: STMR_TOPIC, ctaTmplt: string | any[]): STMR_FORM[] {

    var stmrForms: STMR_FORM[] = []
    // 1. Check if CTA and create/update form
    // 2. Check for associated forms.....
    if (newTopic.CTA_ID) {
      if (ctaTmplt && ctaTmplt.length) {
        for (let i = 0, iLen = ctaTmplt.length; i < iLen; i++) {
          if (ctaTmplt[i].CTA_ID == newTopic.CTA_ID) {

            if (this.prefillData) {
              // Create form for this template with ctaTmplt[i].L_VER_ID
              let newForm: STMR_FORM = <STMR_FORM>{};
              newForm.LID = this.utilityService.guid32();
              newForm.STMR_ID = this.currStmrHeader.STMR_ID;
              newForm.TOPIC_NO = newTopic.TOPIC_NO;
              newForm.FORM_ID = 'New' + this.utilityService.guid32();
              newForm.VER_ID = ctaTmplt[i].L_VER_ID;
              newForm.CRTD_BY = this.prefillData.USER_ID;
              newForm.CRTD_ON = moment.utc().unix();
              newForm.SUBM_BY = this.prefillData.USER_ID;
              newForm.DATE_COMP = moment.utc().unix();
              newForm.COMPANY = this.prefillData.COMP_CODE;
              newForm.RIG_NO = this.prefillData.RIG_NO;
              newForm.COMMENTS = "";
              newForm.FORM_STATUS = AppConstants.VAL_FORM_STATUS.OPEN;
              newForm.FID = this.currStmrHeader.LID;
              newForm.NAME = ctaTmplt[i].NAME;
              newForm.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
              newForm.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
              newForm.P_MODE = "A";
              newForm.LAST_SYNC_USER = this.prefillData.USER_ID
              newForm.SUBM_BY = this.prefillData.USER_ID
              // decoration handled at render time
              stmrForms.push(newForm)
            }
            else {
              this.unviredSDK.logError("TopicsPage", "createOrUpdateFormHeader", "Error while fetching Prefill values stored in memory");
            }
          }
        }
      } else {
        // No Templates associated
        this.unviredSDK.logInfo("TopicsPage", "createOrUpdateFormHeader", "No Template associated with this CTA: " + newTopic.CTA_ID);
      }
    } else {
      this.unviredSDK.logInfo("TopicsPage", "createOrUpdateFormHeader", "This topic is not a CTA: " + JSON.stringify(newTopic, null, 2));
    }
    return stmrForms
  }

  // Validate inputs if create
  validateTopic(): boolean {
    if (this.isEditTopic == AppConstants.BOOL_FALSE) {
      if (!this.topicEntity.topic.TOPIC_ID) {
        this.alertService.showAlert("Alert", "Please select the topic.");
      } else if (this.checkIfCTA() && !this.topicEntity.topic.CTA_ID) {
        this.alertService.showAlert("Alert", "Please select the CTA type.");
      } else if (this.checkIfHSE() && !this.topicEntity.topic.STD_ID) {
        this.alertService.showAlert("Alert", "Please select the HSE type.");
      } else if (!this.topicStart) {
        this.alertService.showAlert("Alert", "Please select the topic start time.");
      } else if (!this.TOPIC_NOTE) {
        this.alertService.showAlert("Alert", "Please enter the topic description.");
      } else if (!this.TOPIC_NOTE.trim()) {
        this.alertService.showAlert("Alert", "Please enter valid topic description.");
      } else if (this.errorMessageForInvalidDateTime.length > 0) {
        this.alertService.showAlert("Alert", this.errorMessageForInvalidDateTime)
      }
      else {
        return true;
      }
    } else {
      if (!this.TOPIC_NOTE) {
        this.alertService.showAlert("Alert", "Please enter the topic description.");
      } else if (!this.TOPIC_NOTE.trim()) {
        this.alertService.showAlert("Alert", "Please enter valid topic description.");
      } else if (this.errorMessageForInvalidDateTime.length > 0) {
        this.alertService.showAlert("Alert", this.errorMessageForInvalidDateTime)
      }
      else {
        return true;
      }
    }
    return false
  }

  // Delete Topic 
  async deleteTopic() {
    // Ask for confirmation
    const alert = await this.alertController.create({
      header: "Delete Topic",
      subHeader: AppConstants.DELETE_FORMS_MSG,
      buttons: [
        {
          text: 'Delete',
          handler: () => {

            // Mark the fields with P_MODE.
            this.topicEntity.topic.P_MODE = "D";
            this.topicEntity.topic.OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE

            // Update Forms
            this.topicEntity.forms = this.topicEntity.forms.map(element => {
              element.P_MODE = 'D'
              element.OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE
              return element
            })

            // Update Form Data
            this.topicEntity.data = this.topicEntity.data.map(element => {
              element.P_MODE = 'D'
              element.OBJECT_STATUS = AppConstants.OBJECT_STATUS.DELETE
              return element
            })

            this.STMRDetailsPage.callbackAfterTopic(this.currStmrHeader, this.topicEntity, this.stmrActionArray);
            this.navCtrl.back();
          }
        },
        {
          text: 'Cancel',
          handler: () => {
            console.log('Disagree clicked');
          }
        }
      ],
    });
    await alert.present();
  }

  // If Topic selected is CTA then display CTA types
  checkIfCTA() {
    let that = this;
    if (that.topicEntity.topic.TOPIC_ID == AppConstants.VAL_CTA_TOPIC_ID) {
      return AppConstants.BOOL_TRUE;
    }
    else {
      that.topicEntity.topic.CTA_ID = "";
      return AppConstants.BOOL_FALSE;
    }
  }

  // If Topic selected is CTA then display CTA types
  checkIfHSE() {
    let that = this;
    if (that.topicEntity.topic.TOPIC_ID == AppConstants.VAL_HSE_TOPIC_ID) {
      return AppConstants.BOOL_TRUE;
    }
    else {
      that.hseDocs = []
      that.topicEntity.topic.STD_ID = "";
      return AppConstants.BOOL_FALSE;
    }
  }

  isValidDoc(doc: any) {
    if (typeof (doc.FILE_NAME) == 'undefined' || doc.FILE_NAME == null || doc.FILE_NAME.length == 0) {
      return false
    }
    return true
  }

  // Open Doc 
  openDoc(doc: any) {

    // Ignore double taps within 'x' seconds.
    let timestamp = new Date().getTime()
    if (timestamp - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
      return
    }
    this.timestampOfLastTap = timestamp

    console.log('TopicsPage', 'openDoc()', 'Opening CTA Document: ' + JSON.stringify(doc, null, 2))

    if (typeof (doc.FILE_NAME) == 'undefined' || doc.FILE_NAME == null || doc.FILE_NAME.length == 0) {
      this.alertService.showAlert(this.translate.instant("Error"), this.translate.instant("This document is not associated with a valid filename. Please contact your adminstrator."))
      console.error("TopicsPage", "openDoc()", "Error while opening Doc: " + doc.NAME + " File name: " + doc.FILE_NAME)
      return
    }

    this.utilityService.getCTALocation().then(ctaLocation => {
      this.displayCTAService.displayCTA(doc, this.ctaDocs, ctaLocation)
    }, error => {
      console.error("TopicsPage", "openDoc()", "Error while getting CTA Location: " + error)
    })
  }

   // Open Doc 
   openDocHSE(doc: any) {

    // Ignore double taps within 'x' seconds.
    let timestamp = new Date().getTime()
    if (timestamp - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
      return
    }
    this.timestampOfLastTap = timestamp

    console.log('TopicsPage', 'openDocHSE()', 'Opening HSE Document: ' + JSON.stringify(doc, null, 2))

    if (typeof (doc.FILE_NAME) == 'undefined' || doc.FILE_NAME == null || doc.FILE_NAME.length == 0) {
      this.alertService.showAlert(this.translate.instant("Error"), this.translate.instant("This document is not associated with a valid filename. Please contact your adminstrator."))
      console.error("TopicsPage", "openDocHSE()", "Error while opening Doc: " + doc.NAME + " File name: " + doc.FILE_NAME)
      return
    }

    this.utilityService.getHSELocation().then(hseLocation => {
      this.displayHSEService.displayHSE(doc, this.hseDocs, hseLocation)
    }, error => {
      console.error("TopicsPage", "openDocHSE()", "Error while getting HSE Location: " + error)
    })
  }

  viewDocInAppBrowser(urlEncodedPath: string) {
    // Copy the file to Documents Directory and check
    this.myBrowser = this.iab.create(urlEncodedPath, "_blank", {
      hardwareback: 'no',
      location: 'no',
      clearcache: 'yes',
      clearsessioncache: 'yes',
      fullscreen: 'yes'
    });

    this.myBrowser.on("loadstop").subscribe((response: any) => {
      this.myBrowser.show()
    })
  }

  onError(error: any) {
    console.log("Error while viewing doc: " + JSON.stringify(error));
  }

  openSTMRFormWithIndex(form: any, index: number) {
    this.indexOfLastOpenedForm = index
    this.openForm(form)
  }

  // Open form
  async openForm(form: any, skipFetchFormData?: any) {

    // Ignore double taps within 'x' seconds.
    let timestamp = new Date().getTime()
    if (timestamp - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
      return
    }
    this.timestampOfLastTap = timestamp

    if (!this.validateTopic()) {
      console.log("TopicsPage", "openForm()", "Topic is not valid. Asking the user to fill the topic first.")
      return
    }

    let that = this,
      tmpHeader = JSON.parse(JSON.stringify(form));
    delete tmpHeader.TMPLT_VER;

    // Get File Name
    try {
      const res = await this.unviredSDK.dbExecuteStatement("Select * from TMPLT_ATTACHMENT where TAG1='" + form.VER_ID + "'");
      if (res.data && res.data.length > 0) {
        if (res.data[0].ATTACHMENT_STATUS != "DOWNLOADED" && res.data[0].ATTACHMENT_STATUS != 2) {
          this.alertService.showAlert("Info", "Downloading Attachment please wait...");
          return;
        }

        if (this.platform.is('electron')) {

          var srcPathFile = res.data[0].LOCAL_PATH,
            root = "ms-appdata:///local/Attachments/",
            rootPath = root + form.VER_ID;
          if (res.data && res.data[0] && res.data[0].TAG3) {
            that.openFileInBrowser(rootPath + '/index.html', form, root, srcPathFile, skipFetchFormData);
          }
          else {
            // Unzip will be handled by SDK on Windows/Electron via unzip used elsewhere if needed
            that.openFileInBrowser(rootPath + '/index.html', form, root, srcPathFile, skipFetchFormData);
            
            var query = "UPDATE TMPLT_ATTACHMENT SET " +
              "TAG3 ='" + form.VER_ID + "'" +
              "WHERE LID = '" + res.data[0].LID + "'";
            try {
              await this.unviredSDK.dbExecuteStatement(query);
            } catch (error) {
              console.error("TopicsPage", "openForm", "Saving the unzipped path failed: " + error);
            }
          }
        } else {
          // Use the SDK's getAttachmentFolderPath method
          const path = await this.unviredSDK.getAttachmentFolderPath();
          let srcPathFile = "file:" + path.data + "/" + res.data[0].FILE_NAME,
            destPath = path.data + '/' + form.VER_ID,
            destPathFile = "file:" + destPath;
          if (res.data && res.data[0] && res.data[0].TAG3) {
            that.openFileInBrowser(destPathFile + '/index.html', form, path.data, srcPathFile, skipFetchFormData);
          } else {
            // Note: Unzip functionality for mobile platforms would need to be implemented
            console.log('Mobile unzip functionality needs to be implemented');
          }
        }
      }
    } catch (error) {
      that.alertService.showAlert("Error", error + " " + error);
      console.error("TopicsPage", "openForm", "Error while fetching Template Attachment from DB : " + error);
    }
  }

  /**
   * Open File in InApp browser
   */
openFileInBrowser(fileLink: string, form: any, attachmentPath: string, zipFilePath: string, skipFetchFormData?: any) {

    this.indexOfLastOpenedFormData = this.topicEntity.data.findIndex((data, index) => {
      return (form.STMR_ID == data.STMR_ID && form.TOPIC_NO == data.TOPIC_NO && form.FORM_ID == data.FORM_ID)
    })
    let formData = this.topicEntity.data[this.indexOfLastOpenedFormData].DATA
    this.viewForm(this.prefillData, formData, fileLink, form, attachmentPath, zipFilePath);
  }

async viewForm(
  prefillData: any,
  actualData: any,
  fileLink: string,
  form: any,
  attachmentPath: string,
  zipFilePath: string
) {
  const that = this;

  // Create the in-app browser instance
  this.myBrowser = this.iab.create(fileLink, "_blank", {
    hardwareback: "no",
    location: "no",
    clearcache: "yes",
    clearsessioncache: "yes",
    toolbar: "no",
    fullscreen: "yes"
  });

  // ====== LOADSTOP EVENT ======
  this.myBrowser.on("loadstop").subscribe((response: any) => {
    console.log("Load Stop Response URL: " + response.url);
    this.myBrowser.show();

    const val = (response.url || "").toLowerCase();
    if (val.includes("#")) {
      // Do nothing if hash in URL
      return;
    }

    // Inject scripts after short delay
    setTimeout(() => {
      this.myBrowser.executeScript({
        code: `loadDefaultValues(${JSON.stringify(prefillData)})`
      });

      if (actualData && actualData !== "null") {
        this.myBrowser.executeScript({
          code: `loadPreviousValues(${actualData})`
        });
      }

      // Determine form status
      let formStatus = "";
      if (form.SYNC_STATUS === AppConstants.SYNC_STATUS.ERROR) {
        formStatus = AppConstants.VAL_FORM_STATUS.INPR;
      } else if (form.FORM_STATUS === "SUBM" || form.FORM_STATUS === "SKIP") {
        formStatus = form.FORM_STATUS;
      } else if (this.currStmrHeader.SYNC_STATUS === AppConstants.SYNC_STATUS.ERROR) {
        formStatus = AppConstants.VAL_FORM_STATUS.INPR;
      } else if (this.currStmrHeader.STMR_STATUS === "SUBM") {
        formStatus = this.currStmrHeader.STMR_STATUS;
      } else if (actualData && actualData !== "null") {
        formStatus = AppConstants.VAL_FORM_STATUS.INPR;
      } else {
        formStatus = AppConstants.VAL_FORM_STATUS.OPEN;
      }

      const status = { FormStatus: formStatus, IsSTMR: AppConstants.BOOL_TRUE };
      this.myBrowser.executeScript({
        code: `setStatus(${JSON.stringify(status)})`
      });
    }, 1200);
  });

  // ====== LOADERROR EVENT ======
  this.myBrowser.on("loaderror").subscribe((response: any) => {
    console.error("Load Error:", response.url, "Message:", response.message);
  });

  // ====== EXIT EVENT ======
  this.myBrowser.on("exit").subscribe((response: any) => {
    console.log("Exit:", response.url, "Message:", response.message);
  });

  // ====== LOADSTART EVENT ======
  this.myBrowser.on("loadstart").subscribe((response: any) => {
    this.ngZone.run(() => {
      (async () => {
        const val = (response.url || "").toLowerCase();

        // --- Save/Submit/Skip actions ---
        if (
          val.includes("submitbuttonclicked") ||
          val.includes("savebuttonclicked") ||
          val.includes("saveandexitbuttonclicked") ||
          val.includes("skipbuttonclicked")
        ) {
          this.formUtilityService.getFormDataFromHTML(this.myBrowser, async (result) => {
            const formSubmit = AppConstants.BOOL_TRUE;
            const isSubmit = val.includes("submitbuttonclicked") ? AppConstants.BOOL_TRUE : AppConstants.BOOL_FALSE;
            const isSkip = val.includes("skipbuttonclicked") ? AppConstants.BOOL_TRUE : AppConstants.BOOL_FALSE;
            const closeBrowser = val.includes("savebuttonclicked") ? AppConstants.BOOL_FALSE : AppConstants.BOOL_TRUE;

            try {
              // 1. Check if in Sent Items
              const resultInSent = await this.unviredSDK.isInSentItem(that.currStmrHeader.LID);
              const isInSentItem = resultInSent.data === true || resultInSent.data === "true";

              if (isInSentItem) {
                this.myBrowser.close();
                this.indexOfLastOpenedFormData = -1;
                this.indexOfLastOpenedForm = -1;

                const alert = await this.alertController.create({
                  header: "Previous request still being reconciled!",
                  message: "Your previous change to this form is being reconciled. Continue editing or discard changes?",
                  cssClass: "data-save-modal",
                  buttons: [
                    {
                      text: "Continue Editing",
                      handler: () => {
                        try {
                          const tmpForm = JSON.parse(JSON.stringify(form));
                          that.openForm(tmpForm, result[0]);
                        } catch {
                          that.alertService.showAlert("Error parsing data", "Click the form again to reopen.");
                        }
                      }
                    },
                    { text: "Discard Changes", role: "cancel" }
                  ]
                });
                await alert.present();
                return;
              }

              // 2. Check if in Outbox
              const resultInOutBox = await this.unviredSDK.isInOutBox(that.currStmrHeader.LID);
              const isInOutBox = resultInOutBox.data === true || resultInOutBox.data === "true";

              if (isInOutBox) {
                this.updateSTMRHeader(form, result[0], prefillData.USER_ID, AppConstants.BOOL_FALSE, isSubmit, AppConstants.BOOL_TRUE, isSkip);
                if (closeBrowser) {
                  this.myBrowser.close();
                  this.indexOfLastOpenedFormData = -1;
                  this.indexOfLastOpenedForm = -1;
                }
                return;
              }

              // 3. Normal send
              this.updateSTMRHeader(form, result[0], prefillData.USER_ID, formSubmit, isSubmit, AppConstants.BOOL_TRUE, isSkip);
              if (closeBrowser) {
                this.myBrowser.close();
                this.indexOfLastOpenedFormData = -1;
                this.indexOfLastOpenedForm = -1;
              }
            } catch (error) {
              console.error("Error checking sync status:", error);
              this.updateSTMRHeader(form, result[0], prefillData.USER_ID, formSubmit, isSubmit, AppConstants.BOOL_TRUE, isSkip);
              if (closeBrowser) {
                this.myBrowser.close();
                this.indexOfLastOpenedFormData = -1;
                this.indexOfLastOpenedForm = -1;
              }
            }
          });
        }

        // --- Back button ---
        else if (val.includes(AppConstants.IN_APP_BROWSER_LISTENER.BACK.toLowerCase())) {
          this.myBrowser.close();
          this.indexOfLastOpenedFormData = -1;
          this.indexOfLastOpenedForm = -1;
        }

        // --- Print button ---
        else if (val.includes("printbuttonclicked")) {
          try {
            const result = await this.myBrowser.executeScript({ code: "generateJSON()" });
            if (result && result.length > 0) {
              this.checkDirectory(attachmentPath, form, zipFilePath, result[0], prefillData.LOGO);
            } else {
              this.myBrowser.close();
              this.indexOfLastOpenedFormData = -1;
              this.indexOfLastOpenedForm = -1;
              this.displayNoDataErr();
            }
          } catch {
            this.myBrowser.close();
            this.indexOfLastOpenedFormData = -1;
            this.indexOfLastOpenedForm = -1;
            this.displayNoDataErr();
          }
        }
      })();
    });
  });
}


  checkDirectory(attachmentPath: string, form: any, zipFilePath: string, jsonData: string, comLogo: string) {
    /**
     * 1. Check if directory exist
     * 2. a. If exist, then remove that directory,
     *       if removed, then call unzip directory
     * 2. b. If does not exist, then call unzip directory
     */

    // 1. Check if directory exist
    this.file.checkDir(attachmentPath, "Print_" + form.VER_ID)
      .then(checkResult => {

        this.unviredSDK.logInfo("TopicsPage", "checkDirectory", "Directory exist." + JSON.stringify(checkResult, null, 2));

        // 2. a.If exist, then remove that directory,
        this.file.removeRecursively(attachmentPath, "Print_" + form.VER_ID)
          .then(removeResult => {

            this.unviredSDK.logInfo("TopicsPage", "checkDirectory", "Directory removed." + JSON.stringify(removeResult, null, 2))

            // if removed, then call unzip directory
            this.unzipDirectory(attachmentPath, form.VER_ID, zipFilePath, jsonData, comLogo);

          })
          .catch(removeError => {

            this.unviredSDK.logInfo("TopicsPage", "checkDirectory", "Directory not removed. Error: " + JSON.stringify(removeError, null, 2))

          })

      }).catch(checkError => {

        this.unviredSDK.logInfo("TopicsPage", "checkDirectory", "Directory does not exist. Error: " + JSON.stringify(checkError, null, 2))

        // 2. b.If does not exist, then call unzip directory
        this.unzipDirectory(attachmentPath, form.VER_ID, zipFilePath, jsonData, comLogo);

      })
  }


  async writeFormData(attachmentPath: string, verId: string, jsonData: string, comLogo: string, zipFilePath: string) {

    let that = this;

    /**
     * 1. Read contents of index.html
     * 2. Replace "<!-- FORM DATA -->" with formData as a script
     * 3. Save the file
     * 4. Open this file in browser
     */

    // 1. Read contents of index.html
    try {
      const fileTextResult = await this.file.readAsText(attachmentPath + "/Print_" + verId, "index.html");

        this.unviredSDK.logInfo("TopicsPage", "writeFormData", "Read File contents success.");
        let fileData = "var formData = {" + jsonData.substring(1, jsonData.length - 1) + "};"
          + "\nvar companyLogo = '" + comLogo + "';"
          + "\nvar shouldPrint = " + AppConstants.BOOL_TRUE + ";",
          res = fileTextResult.replace("<!-- FORM DATA -->", "<!-- FORM DATA -->\n<script>\n" + fileData + "</script>\n<!-- ./FORM DATA -->");

        try {
          const writeToFileResult = await this.file.writeExistingFile(attachmentPath + "/Print_" + verId, "index.html", res);

            this.unviredSDK.logInfo("TopicsPage", "writeFormData", "Write contents to file failed. Error: " + JSON.stringify(writeToFileResult, null, 2));
            // Open this form in IE
            if (this.platform.is('electron')) {

              // Get the Absolute Path from the local sandbox folder.
              // With that construct an absolute path to the print file.
              // WinJS Code.
              let srcPathFile = `${Windows.Storage.ApplicationData.current.localFolder.path}\\Attachments\\Print_${verId}\\index.html`;

                            console.log("TopicsPage", "writeFormData", "Opening the Attachment File Path: " + srcPathFile);

              // Use the SDK's launchFile method
              await this.unviredSDK.launchFile(srcPathFile);
              console.log("TopicsPage", "writeFormData", "File opened.");
            } else {
              // Use the SDK's getAttachmentFolderPath method
              const path = await this.unviredSDK.getAttachmentFolderPath();
              const srcPathFile = path.data + "/" + 'index.html';
              const urlEncodedPath = encodeURI(srcPathFile);
              console.log("Opening the Attachment File Path: " + urlEncodedPath)
              that.viewFileInAppBrowser(urlEncodedPath)
            }
        } catch (writeToFileError) {
          console.error("TopicsPage", "writeFormData", "Write contents to file failed. Error: " + JSON.stringify(writeToFileError, null, 2));
        }
      } catch (fileTextError) {
        console.error("TopicsPage", "writeFormData", "Read file contents failed. Error: " + JSON.stringify(fileTextError, null, 2));
      }
  }

  viewFileInAppBrowser(urlEncodedPath: string) {
    // Copy the file to Documents Directory and check
    this.myBrowser = this.iab.create(urlEncodedPath, "_system", {
      hardwareback: 'no',
      location: 'no',
      clearcache: 'yes',
      clearsessioncache: 'yes',
      fullscreen: 'yes'
    });

    this.myBrowser.on("loadstop").subscribe((response: any) => {
      this.myBrowser.show()
    })
  }

  updateSTMRHeader(form: any, formData: any, loggedInUserId: string, submitToServer?: boolean, isSubmit?: boolean, isQueued?: boolean, isSkipped?: boolean) {
    let tmpHeader: STMR_HEADER = <STMR_HEADER>{},
      dateComp = moment.utc().unix();
    tmpHeader = JSON.parse(JSON.stringify(this.currStmrHeader)),
      tmpHeader.STMR_STATUS = AppConstants.VAL_FORM_STATUS.INPR;
    tmpHeader.DATE_COMP = dateComp;
    tmpHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;

    // Do not update Sync Status if already Queued
    if (!isQueued) {
      tmpHeader.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;
    }

    tmpHeader.LAST_SYNC_USER = loggedInUserId;
    this.currStmrHeader.LAST_SYNC_USER = loggedInUserId;
    this.currStmrHeader.SUBM_BY = loggedInUserId;
    this.currStmrHeader.DATE_COMP = dateComp;
    this.currStmrHeader.TIME_ZONE = this.utilityService.getTimezone()

    this.updateFormsArray(form, formData, loggedInUserId, submitToServer, isSubmit, isQueued, isSkipped);
  }

  //Update form view
  updateFormsArray(
  form: { LID: string; STMR_ID: string; TOPIC_NO: string; FORM_ID: string; },
  formData: string,
  loggedInUserId: string,
  submitToServer = false,
  isSubmit = false,
  isQueued = false,
  isSkipped = false
) {
  const formIndex = this.topicEntity.forms.findIndex(f => f.LID === form.LID);
  const dataIndex = this.topicEntity.data.findIndex(d =>
    d.STMR_ID === form.STMR_ID &&
    d.TOPIC_NO === form.TOPIC_NO &&
    d.FORM_ID === form.FORM_ID
  );

  if (formIndex === -1 || dataIndex === -1) {
    this.unviredSDK.logInfo("TopicsPage", "updateFormsArray()", `Form or Data not found for LID: ${form.LID}`);
    return;
  }

  let status: string;
  if (isSubmit) status = AppConstants.VAL_FORM_STATUS.SUBM;
  else if (isSkipped) status = AppConstants.VAL_FORM_STATUS.SKIP;
  else status = AppConstants.VAL_FORM_STATUS.INPR;

  // --- Update form ---
  const targetForm = this.topicEntity.forms[formIndex];
  targetForm.FORM_STATUS = status;
  targetForm.TIME_ZONE = this.utilityService.getTimezone();
  (targetForm as any).formStatus = this.utilityService.getFormStatusObj(status);
  if (targetForm.OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
    targetForm.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
    targetForm.P_MODE = 'M';
  }

  // --- Update form data ---
  const targetData = this.topicEntity.data[dataIndex];
  targetData.DATA = formData;
  if (targetData.OBJECT_STATUS === AppConstants.OBJECT_STATUS.GLOBAL) {
    targetData.OBJECT_STATUS = AppConstants.OBJECT_STATUS.MODIFY;
    targetData.P_MODE = 'M';
  }

  // --- Add STMR_ACTION if needed ---
  if (isSubmit || isSkipped) {
    const actionCode = isSubmit ? AppConstants.ACTION_CODE.COMPLETE : AppConstants.ACTION_CODE.SKIP;
    const actionObj: STMR_ACTION = {
      STMR_ID: targetForm.STMR_ID,
      FORM_ID: targetForm.FORM_ID,
      ACTION_CODE: actionCode,
      P_MODE: "A",
      FID: targetForm.FID,
      OBJECT_STATUS: AppConstants.OBJECT_STATUS.ADD,
      LID: '',
      SYNC_STATUS: 0
    };
    this.stmrActionArray.push(actionObj);
  }

  // --- Save to DB ---
  this.saveTopicInDB();
}


  // Move to forms Page
  goToFormsPage() {
    this.homePages = FormsPage;
    this.navCtrl.navigateRoot('/forms');
  }

  // Get Rig Details
  async getRigDetFromDB(callback: (data: any) => void) {
    let that = this;
    try {
      const result = await this.unviredSDK.dbSelect(AppConstants.TABLE_RIG_HEADER, {});
      if (result.data && result.data.length > 0) {
        callback(result.data[0]);
      } else {
        callback({});
      }
    } catch (error) {
      console.error("TopicsPage", "getRigDetFromDB", "Error while fetching Rig Header from DB : " + JSON.stringify(error));
      that.alertService.showAlert("Error", JSON.stringify(error));
    }
  }

  openSelectList(isCTA?: boolean) {
    let that = this;
    if (that.isEditTopic || that.isSTMRReadonly(that.currStmrHeader)) {
      return;
    }
    that.confirmWithUser(() => {
      
      that.ngZone.run(() => {
        if (that.TOPIC_NOTE.length == 0) {
          that.topicEntity.topic.TOPIC_NOTE = "";
        }
      
      if (isCTA) {
        // Is CTA
        that.openSelectGenericList(that.ctas, "CTA", "DESCR", "newCtaDesc", "CTA_ID")

      } else {
        // Is TOPIC
        that.openSelectGenericList(that.topics, "Topic", "TOPIC_NAME", "newTopicDesc", "TOPIC_ID")
      }
    });
    })
  }

  openSelectListHSE() {
    let that = this;
    if (that.isEditTopic || that.isSTMRReadonly(that.currStmrHeader)) {
      return;
    }
    that.confirmWithUser(() => {
      
      that.ngZone.run(() => {
        if (that.TOPIC_NOTE.length == 0) {
          that.topicEntity.topic.TOPIC_NOTE = "";
        }
        that.openSelectGenericList(that.hses, "HSE Standards", "DESCR", "newhseDesc", "STD_ID")
    });
    })
  }

  async confirmWithUser(successCallback: () => void) {

    if (this.topicEntity.forms.length == 0) { // No Confirmation Required.
      successCallback()
      return
    }

    // Ask for confirmation
    const alert = await this.alertController.create({
      header: this.translate.instant("Warning"),
      subHeader: this.translate.instant("Changing the topic name / CTA type will delete all the forms associated with this topic. Are you sure you want to continue?"),
      buttons: [
        {
          text: this.translate.instant('Continue'),
          handler: () => {
            successCallback()
          }
        },
        {
          text: this.translate.instant('Cancel'),
          handler: () => {
            console.log('Disagree clicked');
          }
        }
      ],
    });
    await alert.present();
  }

  /**
   * Open Select CTA list
   * @param selectArr The array to be used as select list
   * @param srchStr Search String
   * @param dispKey The key name that holds Description
   * @param localDescKey The key name used to display description
   * @param valueKey The main key name where the value has to be stored
   */
async openSelectGenericList(
  selectArr: any[],
  srchStr: string,
  dispKey: string,
  localDescKey: 'newCtaDesc' | 'newTopicDesc' | 'newhseDesc',
  valueKey: string
) {
  // ✅ Ensure prefillData is loaded
  if (!this.prefillData) {
    this.prefillData = await firstValueFrom(
      this.store.select(selectPrefilledData).pipe(
        filter(data => !!data) // wait until not null/undefined
      )
    );
  }

  const listData: any[] = [];

  if (selectArr) {
    for (let i = 0, iLen = selectArr.length; i < iLen; i++) {
      listData.push({
        DisplayString: selectArr[i][dispKey],
        object: selectArr[i],
        isSelected:
          selectArr[i][dispKey] === (this as any)[localDescKey]
            ? AppConstants.BOOL_TRUE
            : AppConstants.BOOL_FALSE
      });
    }
  }

  const rigType = this.prefillData?.RIG_TYPE || '';
  const rigSubType = this.prefillData?.RIG_SUB_TYPE || '';
  const rigNumber = this.prefillData?.RIG_NO || '';

  if (listData.length === 0) {
    let msg = '';
    switch (srchStr) {
      case 'CTA':
        msg = `No CTAs are configured for your site: ${rigNumber} (${rigType}: ${rigSubType})`;
        break;
      case 'Topic':
        msg = `No topics are configured for your site: ${rigNumber} (${rigType}: ${rigSubType})`;
        break;
      case 'HSE STANDARD':
        msg = `No HSE Standards are configured for your site: ${rigNumber} (${rigType}: ${rigSubType})`;
        break;
    }
    if (msg) {
      this.alertService.showAlert(this.translate.instant('Information'), this.translate.instant(msg));
    }
    return;
  }

  // ✅ Continue with your modal or navigation logic
  const modal = await this.modalController.create({
    component: SelectListPage,
    componentProps: {
      listData,
      listDetails: {
        title: 'Select ' + srchStr,
        searchPlaceHolder: 'Search ' + srchStr,
        selectKey: 'isSelected',
        multiSelect: AppConstants.BOOL_FALSE,
        eventName: 'itemSelected',
        hideCloseBtn: AppConstants.BOOL_TRUE
      },
      theme: this.styleTheme
    }
  });

  await modal.present();

  const { data } = await modal.onDidDismiss();
  if (data && data.DisplayString && data.object) {
    this.ngZone.run(() => {
      (this as any)[localDescKey] = data.DisplayString;
      (this.topicEntity.topic as any)[valueKey] = data.object[valueKey];
      this.topicEntity.forms = [];
      this.topicEntity.data = [];
    });

    if (data.object.CTA_ID) {
      this.defaultDescription = data.object.NAME;
      this.getDocsAndTemplates();
    } else if (data.object.STD_ID) {
      this.defaultDescription = data.object.NAME;
      this.getHSEDocs();
    } else {
      this.newCtaDesc = '';
      this.newhseDesc = '';
      this.topicEntity.topic.CTA_ID = '';
      this.ctaDocs = [];
      this.hseDocs = [];
      this.topicEntity.forms = [];
      this.topicEntity.data = [];
      this.defaultDescription = '';
    }
  }
}


  displayNoDataErr() {
    this.alertService.showAlert("Error reading data!", "There was an error reading data. Please contact your administrator.");
  }

  onModelChange(event: any) {
    // HACK.. 
    // There is always a false change event being trggered out of textarea control.
    // This is because of placeholder text that is set.
    // This is a known issue.
    // Check the known issues section#: https://docs.angularjs.org/api/ng/directive/textarea
    // We are ignoring this event.
    if (this.isFirst) {
      this.isFirst = false
      return
    }
    this.isTopicUpdated = true

    this.ngZone.run(() => {
      this.topicEntity.topic.TOPIC_NOTE = this.defaultDescription + this.TOPIC_NOTE.trim();
    });
  }

  getFormIDForDisplay(formId: string) {
    return formId.startsWith('New') ? this.translate.instant('New') : formId
  }

  // getCTANameFromDB():Promise<string> {
  async getCTANameFromDB(id: string): Promise<string> {
    let that = this;
    return new Promise(async (resolve, reject) => {
      that.ngZone.run(async () => {
        let query = "SELECT DISTINCT NAME FROM CTA_HEADER WHERE CTA_ID='" + id + "'";

        try {
          const result = await this.unviredSDK.dbExecuteStatement(query);
          console.log("TopicsPage", "getCTANameFromDB()", "CTA Name: " + JSON.stringify(result.data));
          if (result.data && result.data.length > 0) {
            for (let i = 0; i < result.data.length; i++) {
              return resolve(result.data[i].NAME);
            }
          } else {
            console.error("TopicsPage", "getCTANameFromDB", "Error while fetching CTA ID from DB : " + JSON.stringify(result.error));
            that.alertService.showAlert("Error", JSON.stringify(result.error));
          }
        } catch (error) {
          console.error("TopicsPage", "getCTANameFromDB", "Error while fetching CTA ID from DB : " + JSON.stringify(error));
          that.alertService.showAlert("Error", JSON.stringify(error));
        }
      });
    });
  }

   // getHSENameFromDB():Promise<string> {
    async getHSENameFromDB(id: string): Promise<string> {
      let that = this;
      return new Promise(async (resolve, reject) => {
        that.ngZone.run(async () => {
          let query = "SELECT DISTINCT NAME FROM HSE_STANDARD_HEADER WHERE STD_ID='" + id + "'";
  
          try {
            const result = await this.unviredSDK.dbExecuteStatement(query);
            console.log("TopicsPage", "getHSENameFromDB()", "HSE Name: " + JSON.stringify(result.data));
            if (result.data && result.data.length > 0) {
              for (let i = 0; i < result.data.length; i++) {
                return resolve(result.data[i].NAME);
              }
            } else {
              console.error("TopicsPage", "getHSENameFromDB", "Error while fetching HSE ID from DB : " + JSON.stringify(result.error));
              that.alertService.showAlert("Error", JSON.stringify(result.error));
            }
          } catch (error) {
            console.error("TopicsPage", "getHSENameFromDB", "Error while fetching HSE ID from DB : " + JSON.stringify(error));
            that.alertService.showAlert("Error", JSON.stringify(error));
          }
        });
      });
    }

  
  computeMaxDate() {
  const currentDate = moment.utc().unix(); // seconds UTC now
  const twentyFourHoursFromSTMRCreatedTime =
    this.currStmrHeader.CRTD_ON + (24 * 3600); // seconds

  if (currentDate < twentyFourHoursFromSTMRCreatedTime) {
    this.maxDate = new Date(currentDate * 1000);
  } else {
    this.maxDate = new Date(twentyFourHoursFromSTMRCreatedTime * 1000);
  }
}
  isCurrentTimePast24HrsFromSTMRCreatedTime() {
  const currentDate = moment.utc().unix();
  const twentyFourHoursFromSTMRCreatedTime =
    this.currStmrHeader.CRTD_ON + (24 * 3600);
  return currentDate > twentyFourHoursFromSTMRCreatedTime;
}

setSTMRCreateDate() {
  this.ngZone.run(() => {
    if (!this.currStmrHeader?.CRTD_ON) return;

    this.stmrCreateDate = new Date(this.currStmrHeader.CRTD_ON * 1000);
    if (isNaN(this.stmrCreateDate.getTime())) return;

    if (this.topicStart === undefined) return;

    // Format creation time display
    this.stmrCreatedTime =
      this.monthNames[this.stmrCreateDate.getMonth()] + ' ' +
      this.stmrCreateDate.getDate() + ' ' +
      this.stmrCreateDate.getFullYear() + ' ' +
      this.stmrCreateDate.getHours() + ':' +
      this.stmrCreateDate.getMinutes();
  });
}

  validateSelectedTime(updated_time: string) {
    let that = this;
    that.ngZone.run(() => {
      var max_topic_time = that.getMaxTopicTime();
      max_topic_time  = new Date(new Date(max_topic_time).getTime()).setSeconds(0)
      console.log("TopicsPage", "validateSelectedTime()", "validateSelectedTime() called");
      var d = new Date(new Date(updated_time).getTime()).setSeconds(0);
      console.log("selected date and time is =" + d)
      console.log("TopicsPage", "validateSelectedTime()", "selected date and time (timestamp) =" + d);

      if (d <  new Date(that.stmrCreateDate.getTime()).setSeconds(0)) {
        console.log("TopicsPage", "validateSelectedTime()", "selected topic date and time is less than this.stmrCreateDate");
        that.exceedMaxTimeErr = false;
        that.belowSTMRTimeErr = true;
        setTimeout(() => {
          that.exceedMaxTimeErr = false;
          that.belowSTMRTimeErr = false;
        }, 2500);

      }
      else if (d > max_topic_time) {
        console.log("TopicsPage", "validateSelectedTime()", "selected topic date and time is more than less than this.stmrCreateDate");
        that.exceedMaxTimeErr = true;
        that.belowSTMRTimeErr = false;
        setTimeout(() => {
          that.exceedMaxTimeErr = false;
          that.belowSTMRTimeErr = false;
        }, 2500);

      }
      else {
        that.isTopicUpdated = true;
        that.exceedMaxTimeErr = false;
        that.belowSTMRTimeErr = false;
      }
    });

  }

  getMaxTopicTime() {
  this.ngZone.run(() => {
    this.time_now = new Date(); // Date object
    
    // Calculate the max allowed topic time (timestamp, ms)
    let maxAllowedValueTimestamp = new Date(this.stmrCreateDate.getTime()).setHours(this.stmrCreateDate.getHours() + 24);
    
    // Clamp to current time if needed
    if (this.time_now.getTime() < maxAllowedValueTimestamp) {
      maxAllowedValueTimestamp = this.time_now.getTime(); // Use current time as max
    }
    // Now maxAllowedValueTimestamp is always a number (milliseconds)
    
    // Format for UI using moment
    this.maxAllowedTopicTime = moment(maxAllowedValueTimestamp).format('MMM DD YYYY HH:mm');
  });
  return this.maxAllowedTopicTime;
}


  setMaxTopicTime() {
  this.ngZone.run(() => {
    this.topicStart = this.maxDate.toISOString();
    this.isTopicUpdated = true;
    this.errorMessageForInvalidDateTime = '';
    this.topicEntity.topic.TOPIC_START = Math.floor(this.maxDate.getTime() / 1000).toString();
  });
}

  isAllowedToUpdateTopicTime() {
  return !this.currentTimePast24HrsFromSTMRCreatedTime &&
         !this.isSTMRReadonly(this.currStmrHeader);
}


async unzipDirectory(
  attachmentPath: string,
  verId: string,
  zipFilePath: string,
  jsonData: string,
  comLogo: string
) {
  try {
    let zipData: Uint8Array;

    // ----- Electron -----
    if ((window as any).process?.versions?.electron) {
      const fs = (window as any).electronAPI.fs;
      zipData = new Uint8Array(fs.readFileSync(zipFilePath));

    // ----- Cordova / iOS -----
    } else {
      const fileEntry = await this.file.resolveLocalFilesystemUrl(zipFilePath);
      const fileObj: any = await new Promise((resolve, reject) =>
        (fileEntry as any).file(resolve, reject)
      );
      const arrayBuffer = await fileObj.arrayBuffer();
      zipData = new Uint8Array(arrayBuffer);
    }

    // ----- Unzip -----
    const files = unzipSync(zipData); // { filename: Uint8Array }

    // ----- Save extracted files -----
    for (const [filename, content] of Object.entries(files)) {
      if ((window as any).process?.versions?.electron) {
        const fs = (window as any).electronAPI.fs;
        const path = (window as any).electronAPI.path;

        const destPath = path.join(attachmentPath, `Print_${verId}`, filename);
        fs.mkdirSync(path.dirname(destPath), { recursive: true });
        fs.writeFileSync(destPath, content);

      } else {
        const dirPath = `${attachmentPath}Print_${verId}/`;
        await this.file.createDir(attachmentPath, `Print_${verId}`, true);

        // Convert Uint8Array → ArrayBuffer for Cordova File plugin
         const arrayBuffer: ArrayBuffer = new Uint8Array(content as Uint8Array).buffer;
        await this.file.writeFile(dirPath, filename, arrayBuffer, { replace: true });
      }
    }

    console.log('TopicsPage', 'unzipDirectory', 'File unzipped.');
    this.writeFormData(attachmentPath, verId, jsonData, comLogo, zipFilePath);

  } catch (error) {
    console.error('Error unzipping with fflate:', error);
  }
}


goBack() {
  this.modalController.dismiss();
}

}

