import { Inject, Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import * as uuid from 'uuid';
import { BehaviorSubject, from, Observable } from 'rxjs';
import { File } from '@awesome-cordova-plugins/file/ngx'
import { Store } from '@ngrx/store';
import { firstValueFrom } from 'rxjs';
import { AppState } from '../store/app.state';
import { selectRigLoadedFromDb, selectRigData, selectTemplatesLoadedFromDb, selectAllTemplates } from '../store/store.selector';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstants } from '../constants/appConstants';
import { SETTING_HEADER } from 'src/models/SETTING_HEADER';
import moment from 'moment-timezone';
import { unzipSync } from 'fflate';
import path from 'path';
@Injectable({
  providedIn: 'root'
})
export class UtilityService {
   
  private currentRunModeValue?: String;
  private langSubject = new BehaviorSubject<string>(localStorage.getItem('lang') || 'en');
  lang$ = this.langSubject.asObservable();

  constructor(private translate: TranslateService , private unviredSdk: UnviredCordovaSDK, private store: Store<AppState>, private file: File,) {
    this.setLanguage(this.langSubject.value); // initialize
  }

  changeLanguage(lang: string) {
    localStorage.setItem('lang', lang);
    this.setLanguage(lang);
    this.langSubject.next(lang);
  }

  private setLanguage(lang: string) {
    this.translate.use(lang);
    document.dir = lang === 'ar' ? 'rtl' : 'ltr';
  }

  getCurrentLang(): Observable<string> {
    return this.lang$;
  }

callGetMessages(): Observable<any> {
  return new Observable((observer) => {
    try {
      const response = this.unviredSdk.getMessages();
      observer.next(response);
      observer.complete();
    } catch (error) {
      observer.error(error);
    }
  });
}
public guid32() {
		let guid = uuid.v4();
		let guid32 = guid.replace(/-/g, "");
		return guid32;
	}

public returnDisplayDate(time: any, fromFormat?: string, toFormat?: string): string {
  try {
    if (!time) return '';
    toFormat = toFormat || 'MMM DD YYYY';
    if (fromFormat) {
      // Use provided format if specified
      return moment.utc(time, fromFormat).local().format(toFormat);
    } else {
      // Let moment guess ISO8601/Javascript date
      return moment.utc(time).local().format(toFormat);
    }
  } catch (e) {
    this.unviredSdk.logError("utilityService", "returnDisplayDate", "Invalid Date format");
    return '';
  }

}
getTimeZone(){
const timezone = moment.tz.guess();
return timezone
}
/**
   * Returns new STMR ID in format: 'STMRYYYYMMDDRIGHHmmss000001'
   * @param currIndex Number of current STMRs (for incrementing index)
   * @param rigNo Rig number to embed in ID
   */
  public genStmrId(currIndex: number = 0, rigNo: number | string = 0): string {
    const time = moment();

    const datePart = time.format('YYYYMMDD');
    const timePart = time.format('HHmmss');
    const paddedRigNo = this.addPositiveZeros(rigNo.toString(), 3);
    const paddedIndex = this.addPositiveZeros((currIndex + 1).toString(), 6);

    return `STMR${datePart}${paddedRigNo}${timePart}${paddedIndex}`;
  }

  /**
   * Pads a number with leading zeros to reach the desired length.
   * @param value Number or string to pad
   * @param length Desired total length after padding
   */
  public addPositiveZeros(value: string | number, length: number): string {
    return value.toString().padStart(length, '0');
  }

  private isCompanyLogoSet: boolean = AppConstants.BOOL_FALSE;

  /**
   * Returns Promise<boolean> indicating whether company logo is set.
   */
  public checkCompanyLogoStatus(): Promise<boolean> {
    return Promise.resolve(this.isCompanyLogoSet);
  }
  /**
   * Returns true if Rig data and Templates have been fully loaded from DB.
   */
  public async checkIfAllInitDataDownloaded(): Promise<boolean> {
    const rigLoaded = !!(await firstValueFrom(this.store.select(selectRigLoadedFromDb)));
    const rigData = await firstValueFrom(this.store.select(selectRigData));
    const templatesLoaded = !!(await firstValueFrom(this.store.select(selectTemplatesLoadedFromDb)));
    const templates = await firstValueFrom(this.store.select(selectAllTemplates));

    const isValidRig = rigLoaded && !!rigData?.RIG_NO;
    const isValidTemplates = templatesLoaded && Array.isArray(templates) && templates.length > 0;

    return isValidRig && isValidTemplates;
  }

/**
 * Returns Status description object based on sync status
 * @param syncStatus sync status code (0: NONE, 1: QUEUED, 2: SENT, 3: ERROR)
 */
getSyncStatusObj(syncStatus?: number): { descr?: string; color?: string } {
  let statusDescObj: { descr?: string; color?: string } = {};
  // SYNC_STATUS can be NONE: 0, QUEUED: 1, SENT: 2, ERROR: 3
  // If NONE: 0 do not display send {}
  switch (syncStatus) {
    case 1:
      statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.QUEUED;
      break;
    case 2:
      statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.SENT;
      break;
    case 3:
      statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.ERROR;
      break;
    default:
      break;
  }
  return statusDescObj;
}

  // Helpers used by TopicsPage
  public sortArray<T extends { [key: string]: any }>(array: T[], key: string): T[] {
    if (!Array.isArray(array)) return [] as T[];
    return array.slice().sort((a, b) => {
      const av = (a?.[key] ?? '').toString().toLowerCase();
      const bv = (b?.[key] ?? '').toString().toLowerCase();
      return av.localeCompare(bv);
    });
  }

  public getTopicName(topics: any[], topicId: string): string {
    const t = (topics || []).find(x => x?.TOPIC_ID === topicId);
    return t?.TOPIC_NAME || '';
  }

  public getCTADesc(ctas: any[], ctaId: string): string {
    const c = (ctas || []).find(x => x?.CTA_ID === ctaId);
    return c?.DESCR || c?.NAME || '';
  }

  public getHSEDesc(hses: any[], stdId: string): string {
    const h = (hses || []).find(x => x?.STD_ID === stdId);
    return h?.DESCR || h?.NAME || '';
  }

  public getReleaseVersionDet(versions: any[], keys: string[]): any {
    if (!Array.isArray(versions) || versions.length === 0) return {};
    const sorted = versions.slice().sort((a, b) => {
      const av = Number(a?.VER_NO ?? 0);
      const bv = Number(b?.VER_NO ?? 0);
      return bv - av;
    });
    const latest = sorted[0] || {};
    const out: any = {};
    keys.forEach(k => out[k] = latest[k]);
    return out;
  }

  /**
   * Get local timezone identifier
   */
  public getTimezone(): string {
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC';
    } catch {
      return 'UTC';
    }
  }

  public async getCTALocation(): Promise<string> {
    try {
      const res = await this.unviredSdk.dbSelect(AppConstants.TABLE_SETTING_HEADER, {} as any);
      const row = (res?.data || []).find((r: any) => r?.KEY === AppConstants.SETTING_CTA_LOCAL_PATH);
      return row?.VAL || '';
    } catch {
      return '';
    }
  }

  public async getHSELocation(): Promise<string> {
    try {
      const res = await this.unviredSdk.dbSelect(AppConstants.TABLE_SETTING_HEADER, {} as any);
      const row = (res?.data || []).find((r: any) => r?.KEY === AppConstants.SETTING_HSE_LOCAL_PATH);
      return row?.VAL || '';
    } catch {
      return '';
    }
  }

  /**
   * Returns Status description object based on form status
   */
  public getFormStatusObj(status: any) {
    let statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.OPEN;
    switch (status) {
      case AppConstants.VAL_FORM_STATUS.OPEN:
      default:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.OPEN;
        break;
      case AppConstants.VAL_FORM_STATUS.INPR:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.INPR;
        break;
      case AppConstants.VAL_FORM_STATUS.SUBM:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.SUBM;
        break;
      case AppConstants.VAL_FORM_STATUS.CAN_BE_SUBM:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.CAN_BE_SUBM;
        break;
      case AppConstants.VAL_FORM_STATUS.SKIP:
        statusDescObj = AppConstants.VAL_FORM_STATUS_DESCR.SKIP;
        break;
    }
    return statusDescObj;
  }

  /**
   * Check if app is running in test automation mode (cached)
   */
  public async isAppRunningInTestAutomationMode(): Promise<boolean> {
    if (this.currentRunModeValue === AppConstants.SETTING_MODE_TEST_AUTOMATION) return true;
    if (this.currentRunModeValue === AppConstants.SETTING_MODE_PRODUCTION_RUN) return false;
    try {
      const result = await this.unviredSdk.dbSelect(AppConstants.TABLE_SETTING_HEADER, { NAME: AppConstants.SETTING_RUN_MODE } as any);
      if (result.type === ResultType.success && result.data && result.data.length > 0) {
        const settingsHeader = result.data[0] as SETTING_HEADER;
        this.currentRunModeValue = (settingsHeader as any).VALUE;
      } else {
        this.currentRunModeValue = AppConstants.SETTING_MODE_PRODUCTION_RUN;
      }
      return this.currentRunModeValue === AppConstants.SETTING_MODE_TEST_AUTOMATION;
    } catch {
      return false;
    }
  }

  /**
   * Delete persisted app state file, if present
   */
  public async deleteAppState(): Promise<void> {
    try {
      const basePath: string = this.file.dataDirectory;
      await this.file.removeFile(basePath, 'lastSavedForm.json');
    } catch {
      // ignore
    }
  }

public getAttachmentAsArrayBuffer(path: string): Promise<ArrayBuffer> {
    return new Promise(async (resolve, reject) => {
      const directory = path.substring(0, path.lastIndexOf('/'));
      const filename = path.substring(path.lastIndexOf('/') + 1);
      try {
        const buffer = await this.file.readAsArrayBuffer(directory, filename);
        resolve(buffer);
      } catch (error) {
        this.unviredSdk.logError("UtilityService", "getAttachment", `Error reading file from ${path}: ${JSON.stringify(error)}`);
        reject(error);
      }
    });
  }


  async unzipTheFileAndWriteFileContent(attachmentAsArrayBufferReceived: ArrayBuffer, folderName: string, pathResult: string, jsonData: string, comLogo: string){
    console.log('folder name in unzip is ' , folderName , pathResult);
    const uint8 = new Uint8Array(attachmentAsArrayBufferReceived);
  
            // Unzip
            const files = unzipSync(uint8);
            // files is an object: { [filename: string]: Uint8Array }
            for (const [filename, content] of Object.entries(files)) {
              // Skip directory entries
              if (filename.endsWith('/')) continue;
  
              // Ensure parent directories exist for nested files
              const lastSlash = filename.lastIndexOf('/');
              if (lastSlash > -1) {
                const parentDir = filename.substring(0, lastSlash);
                try {
                  await this.file.createDir(pathResult + '/' + folderName, parentDir, true);
                } catch (dirErr) {
                  // Ignore if already exists
                }
              }
  
              // Write file
              if(folderName.startsWith('Print_')){

       this.file.readAsArrayBuffer(pathResult + folderName, "index.html")
  .then(async (arrayBuffer: ArrayBuffer) => {
    this.unviredSdk.logInfo("FormsPage", "writeFormData", "Read File as ArrayBuffer success.");

    // Convert ArrayBuffer → string
    const decoder = new TextDecoder("utf-8");
    const fileTextResult = decoder.decode(arrayBuffer);

    // Inject form data
    const fileData =
      `var formData = {${jsonData.substring(1, jsonData.length - 1)}};\n` +
      `var companyLogo = '${comLogo}';\n` +
      `var shouldPrint = ${AppConstants.BOOL_TRUE};`;

    const res = fileTextResult.replace(
      "<!-- FORM DATA -->",
      `<!-- FORM DATA -->\n<script>\n${fileData}\n</script>\n<!-- ./FORM DATA -->`
    );

    // Convert string back to ArrayBuffer for writing
    const encoder = new TextEncoder();
    const updatedBuffer = encoder.encode(res);

    await this.file.writeFile(pathResult + '/' + folderName, filename, updatedBuffer.buffer, { replace: true });

    this.unviredSdk.logInfo("FormsPage", "writeFormData", "Write File success.");
  })
  .catch(fileTextError => {
    this.unviredSdk.logInfo("FormsPage", "writeFormData", "Read File as ArrayBuffer failed. Error: " + JSON.stringify(fileTextError, null, 2));
  });

              }else{
                    try {
                await this.file.writeFile(pathResult + '/' + folderName, filename, content.buffer, { replace: true });
                this.unviredSdk.logInfo('UtilityService', 'unzipTheFileAndWriteFile', `File '${filename}' written in directory '${folderName}'.`);
              } catch (writeError) {
                this.unviredSdk.logError('UtilityService', 'unzipTheFileAndWriteFile', `Error writing file '${filename}'`)
              }
              }
          
            }
}
}




