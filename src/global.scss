/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import "@ionic/angular/css/display.css";

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";
@import '@fortawesome/fontawesome-free/css/all.min.css';
/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
@import "@ionic/angular/css/palettes/dark.class.css"; 
// @import '@ionic/angular/css/palettes/dark.system.css';

ion-toolbar.custom-toolbar {
  --background: var(--ion-color-primary);
  --color: white;
}

.white-icon {
  color: white;
}

.left-title {
  text-align: start;
  justify-content: start;
  display: flex;
  align-items: center;
  color: white;
}

.custom-menu {
   --background: var(--ion-color-primary);
  color: white;
}

.custom-menu ion-item {
  --color: white; /* Text color */
  font-weight:bold;
}

.custom-menu ion-toolbar {
  --background: var(--ion-color-primary);
  --color: white;
}

.custom-menu ion-title {
  color: white;
}

.full-screen-modal {
  --width: 100vw;
  --height: 100vh;
  width: 100vw !important;
  height: 100vh !important;
  max-width: 100vw !important;
  max-height: 100vh !important;
  top: 0 !important;
  left: 0 !important;
  border-radius: 0 !important;
  padding: 0 !important;
  margin: 0 !important;
  position: fixed !important;
  z-index: 9999;
  display: flex;
  flex-direction: column;
}

.large-modal ::part(backdrop) {
  background: rgba(0, 0, 0, 0.4);
}

.large-modal::part(content) {
  width: 90vw;
  height: 80vh;
  max-width: 850px;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
}

