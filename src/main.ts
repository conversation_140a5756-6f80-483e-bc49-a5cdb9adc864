import { bootstrapApplication, BrowserModule } from '@angular/platform-browser';
import { RouteReuseStrategy, provideRouter, withPreloading, PreloadAllModules } from '@angular/router';
import { IonicRouteStrategy, provideIonicAngular } from '@ionic/angular/standalone';

import { routes } from './app/app.routes';
import { AppComponent } from './app/app.component';
import { provideStore } from '@ngrx/store';
import { counterReducer, formsReducer, notificationReducer, prefilledReducer, progressReducer, rigReducer, templateReducer } from './app/store/store.reducer';
import { provideEffects } from '@ngrx/effects';
import { StoreEffects } from './app/store/store.effects';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { importProvidersFrom } from '@angular/core';
import { InAppBrowser } from '@awesome-cordova-plugins/in-app-browser/ngx';
import { Deeplinks } from '@awesome-cordova-plugins/deeplinks/ngx';
import { File } from '@awesome-cordova-plugins/file/ngx';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { DataService } from './app/services/data.service';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { SplashScreen } from '@awesome-cordova-plugins/splash-screen/ngx';
import { NotificationService } from './app/notification.service';
import { PrintSTMRFormService } from './app/services/printStmr.service';
import { syncReducer } from './app/store/store.reducer';
import { WebView } from '@awesome-cordova-plugins/ionic-webview/ngx';

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, './assets/i18n/', '.json');
}

bootstrapApplication(AppComponent, {
  providers: [
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    provideHttpClient(withInterceptorsFromDi()),
    provideIonicAngular(),
    BrowserModule,
    File,
    InAppBrowser ,
    importProvidersFrom(
      TranslateModule.forRoot({
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient],
        },
      })
    ),
    provideRouter(routes, withPreloading(PreloadAllModules)),
    // Register all your reducers here
    provideStore({ counter: counterReducer, rig: rigReducer , notification: notificationReducer , template: templateReducer , progress: progressReducer , prefilled: prefilledReducer, sync: syncReducer , forms: formsReducer }),

    // Register all your effects here
    provideEffects(StoreEffects),
    DataService,
    Device,
    InAppBrowser,
    WebView,
    UnviredCordovaSDK,
    SplashScreen,
    NotificationService,
    PrintSTMRFormService
  ],
});
